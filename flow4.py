import pandas as pd
import json
import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import openai
from pathlib import Path
import sys
import os
from datetime import datetime
from dotenv import load_dotenv

class StrictMarkdownPromptEngine:
    @staticmethod
    def create_individual_summary_prompt(summaries: List[str], entities_list: List[str], reasoning_list: List[str], business_context: str = "") -> str:
        summaries_text = "\n\n".join([f"ANALYSIS {i+1}: {summary}" for i, summary in enumerate(summaries)])
        entities_text = "\n".join([f"ENTITIES {i+1}: {entities}" for i, entities in enumerate(entities_list) if entities])
        reasoning_text = "\n\n".join([f"REASONING {i+1}: {reasoning}" for i, reasoning in enumerate(reasoning_list) if reasoning])

        context_section = f"\n\nBUSINESS CONTEXT:\n{business_context}" if business_context else ""

        return f"""
You are a Yazaki supply chain analyst. You MUST output ONLY markdown format with bullet points.

STRICT MARKDOWN OUTPUT REQUIREMENTS:
- You MUST start with "## Individual Analysis"
- You MUST use ONLY bullet points with "•" symbol
- You MUST use markdown bold formatting with "**" for key terms
- You MUST have exactly 3 bullet points maximum
- Each bullet point MUST be 15 words or less
- If insufficient data, output: "• **Insufficient Data**: Cannot generate meaningful analysis without additional context"

REQUIRED MARKDOWN FORMAT:
## Individual Analysis

• **Key Finding**: [Main insight about component demand/supply]
• **Impact**: [Effect on production planning or customer fulfillment]
• **Action Required**: [Specific next steps for supply chain team]

CRITICAL QUANTITATIVE REQUIREMENTS:
- ALWAYS include specific numbers, percentages, part counts, fill rates, volumes, dates
- Extract exact part numbers (e.g., PT00876922-E, 1109004-05-G)
- Include precise metrics (e.g., "95.92% fill rate", "56 distinct parts", "9.01% performance")
- Mention specific timeframes (e.g., "2023 vs 2024", "February 2024", "6 months")
- Reference exact customer names (Tesla, Rivian R1S, Model 3 H LV, Cyber Truck HV)
- Use Yazaki terminology: components, harnesses, connectors, assemblies, production lines

CONVERSATION DATA:
{summaries_text}

{entities_text if entities_text else ""}

{reasoning_text if reasoning_text else ""}
{context_section}

CRITICAL: Output ONLY the markdown format as specified above. Do not include any other text, explanations, or formatting.
"""

    @staticmethod
    def create_session_summary_prompt(individual_summaries: List[str], session_id: str, business_context: str = "") -> str:
        summaries_text = "\n\n".join([f"SUMMARY {i+1}:\n{summary}" for i, summary in enumerate(individual_summaries)])
        context_section = f"\n\nBUSINESS CONTEXT:\n{business_context}" if business_context else ""

        return f"""
You are a Yazaki supply chain director. You MUST output ONLY markdown format with bullet points.

STRICT MARKDOWN OUTPUT REQUIREMENTS:
- You MUST start with "## Supply Chain Analysis Summary"
- You MUST use ONLY bullet points with "•" symbol
- You MUST use markdown bold formatting with "**" for key terms
- You MUST have exactly 3 bullet points maximum
- Each bullet point MUST be 20 words or less
- If insufficient data, output: "• **Data Limitation**: Analysis incomplete due to insufficient context in source conversations"

REQUIRED MARKDOWN FORMAT:
## Supply Chain Analysis Summary

• **Primary Issue**: [Main supply chain challenge or opportunity]
• **Customer Impact**: [Specific effect on key automotive customers]
• **Recommended Action**: [Executive decision required for supply chain optimization]

EXECUTIVE SYNTHESIS REQUIREMENTS:
- Consolidate individual summaries into cohesive supply chain insights with ALL quantitative data
- ALWAYS include specific numbers, percentages, part counts, fill rates, volumes, dates
- Extract exact part numbers (e.g., PT00876922-E, 1109004-05-G) and performance metrics
- Focus on Yazaki's automotive component business impact with measurable data
- Highlight customer-specific issues (Tesla, Rivian, etc.) with precise metrics and timelines

INDIVIDUAL SUMMARIES:
{summaries_text}
{context_section}

CRITICAL: Output ONLY the markdown format as specified above. Do not include any other text, explanations, or formatting.
"""

    @staticmethod
    def create_title_generation_prompt(main_summary: str, session_id: str, business_context: str = "") -> str:
        context_section = f"\n\nBUSINESS CONTEXT:\n{business_context}" if business_context else ""

        return f"""
Generate a concise title for this Yazaki supply chain analysis session.

TITLE REQUIREMENTS:
- 3-6 words maximum
- Include customer name (Tesla, Rivian, etc.) when relevant
- Focus on supply chain action: Analysis, Forecast, Optimization, Review
- Use Yazaki terminology: Components, Demand, Fulfillment, Supply

EXAMPLES:
- "Tesla Component Demand Analysis"
- "Rivian Supply Chain Review"
- "Component Forecast Optimization"

SESSION DATA:
{main_summary}
{context_section}

CRITICAL: Output ONLY the title. Do not include any other text, explanations, or formatting.
"""

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class SessionSummary:
    session_id: str
    individual_summaries: List[str]
    main_summary: str
    title: str
    message_count: int
    processing_time: float
    total_input_tokens: int
    total_output_tokens: int
    individual_input_tokens: List[int]
    individual_output_tokens: List[int]
    session_summary_input_tokens: int
    session_summary_output_tokens: int
    title_input_tokens: int
    title_output_tokens: int

class LLMClient:
    def __init__(self, api_key: str, model_name: str = "gpt-4.1-nano-2025-04-14"):
        self.api_key = api_key
        self.model_name = model_name
        self.client = openai.OpenAI(api_key=api_key)
        self.prompt_engine = StrictMarkdownPromptEngine()

    def count_tokens(self, prompt: str) -> int:
        return len(prompt.split()) * 1.3

    def generate_content(self, prompt: str, max_retries: int = 3) -> Tuple[str, int, int]:
        input_tokens = self.count_tokens(prompt)

        for attempt in range(max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model_name,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=4000,
                    temperature=0.7
                )

                output_tokens = response.usage.completion_tokens if response.usage else 0
                input_tokens = response.usage.prompt_tokens if response.usage else input_tokens

                logger.info(f"Token usage - Input: {input_tokens}, Output: {output_tokens}")
                return response.choices[0].message.content, input_tokens, output_tokens

            except Exception as e:
                logger.error(f"Error generating content (attempt {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(2)

class AdvancedPromptEngine:
    @staticmethod
    def create_individual_summary_prompt(questions: List[str], contexts: List[str], summaries: List[str], reasoning_list: List[str], entities_list: List[str]) -> str:
        if len(questions) == 1:
            return f"""
You are a senior strategic business consultant with deep expertise in comprehensive question-answer analysis and strategic insight generation. Your responsibility is to analyze complex business question-answer interactions while capturing specific entities (names, products, companies, technologies, etc.) to produce thorough analytical summaries that preserve concrete details and entity information.

COMPREHENSIVE BUSINESS ANALYSIS PROTOCOL WITH ENTITY CAPTURE:

STEP 1: SPECIFIC ENTITY IDENTIFICATION AND STRATEGIC MAPPING
Extract and preserve all specific named entities from the complete question-answer interaction:
- Identify specific people names, job titles, roles, executives, and decision-makers mentioned across question, context, answer, and reasoning
- Capture exact product names, service names, brand names, model numbers, SKUs, and technology platforms referenced
- Extract company names, organization names, department names, business units, subsidiary names, and team designations
- Note specific technology names, software platforms, systems, tools, databases, and methodologies used or discussed
- Identify location names, geographic regions, market segments, facilities, offices, and operational areas
- Capture financial figures, metrics, percentages, dates, timelines, budgets, and quantitative data mentioned
- Extract project names, initiative titles, program designations, campaign names, and strategic effort identifiers
- Note regulatory frameworks, compliance standards, governance structures, policy names, and legal requirements
- Identify competitor names, vendor names, partner organizations, client names, and external stakeholders
- Capture specific processes, methodologies, frameworks, strategic approaches, and business models mentioned

STEP 2: BUSINESS QUESTION ANALYSIS WITH ENTITY CONTEXT PRESERVATION
Conduct comprehensive analysis of the business question while preserving entity specifics:
- Parse question exact wording to identify core business information need, strategic decision requirement, and competitive intent mentioning specific entities, products, or organizational units
- Determine question type and analytical complexity including factual strategic inquiry, competitive analysis, operational assessment, or strategic decision-making support affecting particular entities
- Identify specific business domain, functional area, market segment, or organizational scope referencing named departments, products, or market segments
- Extract explicit and implicit requirements, constraints, success criteria, or strategic objectives involving particular entities, technologies, or organizational capabilities
- Assess decision-making context, urgency level, strategic importance, and organizational impact mentioning specific stakeholders, timelines, or business priorities
- Evaluate complexity, scope, and analytical sophistication required referencing particular analytical tools, methodologies, or expertise areas
- Identify intended audience, decision-makers, stakeholders, and beneficiaries mentioning specific roles, departments, or organizational levels
- Assess potential business functions, departments, organizational levels, or market participants representing specific entities in the solution framework
- Determine strategic impact levels ranging from entity-specific operational improvement to ecosystem-wide transformation involving particular organizational units or market segments
- Consider compliance, regulatory, risk management, or governance implications affecting specific entities, frameworks, or regulatory requirements

STEP 3: CONTEXTUAL ENVIRONMENT ANALYSIS WITH ENTITY RELATIONSHIP MAPPING
Examine business context while preserving comprehensive entity relationship information:
- Analyze background information, historical precedents, existing systems, and established processes mentioning specific technologies, systems, organizational units, or historical initiatives
- Identify organizational constraints, policies, governance factors, and cultural considerations referencing particular governance structures, policy frameworks, or organizational characteristics
- Map market conditions, competitive landscape, industry dynamics, and external pressures involving specific competitors, market segments, regulatory bodies, or industry factors
- Assess available resources, organizational capabilities, technological infrastructure, and capacity constraints mentioning specific assets, systems, competencies, or resource limitations
- Examine temporal elements including past events, current assessments, and future projections referencing specific timelines, milestones, historical events, or strategic planning horizons
- Identify interconnected systems, dependencies, integration points, and coordination requirements involving named systems, processes, organizational units, or technology platforms
- Map performance metrics, benchmarks, success indicators, and strategic measurement frameworks mentioning specific KPIs, targets, measurement systems, or performance standards
- Assess risk factors, mitigation strategies, contingency requirements, and strategic adjustment mechanisms referencing particular risk categories, mitigation approaches, or contingency plans
- Evaluate how contextual environment creates opportunities, challenges, constraints, or strategic advantages involving specific market conditions, competitive factors, or organizational capabilities
- Consider cultural, organizational, change management, and stakeholder dynamics referencing particular cultural factors, change initiatives, or stakeholder groups

STEP 4: SOLUTION AND ANSWER EVALUATION WITH ENTITY-SPECIFIC IMPACT ASSESSMENT
Analyze solution quality while preserving entity ecosystem information:
- Examine completeness, accuracy, relevance, and strategic alignment of provided answer mentioning specific recommendations, strategic decisions, and entity-specific implications
- Identify key recommendations, strategic decisions, and action items that specifically address named entities, particular capabilities, organizational units, or strategic objectives
- Extract quantitative data including metrics, measurements, financial figures, and performance indicators mentioning specific amounts, percentages, timelines, or targets
- Evaluate qualitative insights including strategic directions, methodologies, and frameworks referencing particular approaches, strategic frameworks, or implementation methodologies
- Assess implementation feasibility, resource requirements, timeline realism, and execution complexity involving specific resources, organizational capabilities, systems, or timeline considerations
- Determine business value delivered including immediate actionable items and long-term strategic guidance mentioning specific benefits, value creation mechanisms, or strategic outcomes
- Assess potential return on investment, cost savings, efficiency improvements, and competitive advantage creation involving specific financial impacts, efficiency gains, or competitive benefits
- Evaluate risk mitigation effectiveness, compliance improvements, and strategic positioning enhancement referencing particular risk reductions, compliance benefits, or positioning advantages
- Analyze scalability potential, sustainability factors, long-term viability, and strategic resilience involving specific scalability mechanisms, sustainability factors, or strategic assets
- Map entity-specific benefits, costs, implementation requirements, adoption readiness, and strategic success factors across particular stakeholders, organizational units, or implementation areas

STEP 5: REASONING VALIDATION WITH STRATEGIC COHERENCE ASSESSMENT
Validate analytical framework while preserving entity-specific strategic logic:
- Examine logical flow, analytical rigor, and strategic coherence mentioning specific analytical methodologies, reasoning approaches, or strategic frameworks used
- Identify analytical tools, evaluation models, and assessment criteria referencing particular tools, models, evaluation frameworks, or assessment approaches
- Assess quality, relevance, depth, and comprehensiveness of supporting evidence mentioning specific data sources, research references, benchmarks, or evidence types
- Validate reasonableness of assumptions, constraints, and risk considerations affecting particular entities, organizational capabilities, market conditions, or strategic factors
- Evaluate thoroughness of alternative options considering specific alternatives, strategic options, implementation approaches, or solution variations
- Analyze cause-and-effect relationships, logical connections, and strategic dependencies involving particular causal factors, logical relationships, or strategic interdependencies
- Assess implementation feasibility, timeline accuracy, and execution viability referencing specific implementation factors, timeline considerations, or execution requirements
- Review risk mitigation strategies, contingency planning, and strategic adjustment mechanisms mentioning particular risk mitigation approaches, contingency options, or adjustment mechanisms
- Evaluate alignment with business objectives, strategic priorities, and competitive landscape involving specific objectives, strategic priorities, competitive factors, or market positioning goals
- Consider market timing, competitive positioning, and strategic opportunity optimization referencing particular timing factors, competitive advantages, or strategic opportunities

INPUT DATA FOR COMPREHENSIVE ANALYSIS:
QUESTION: {questions[0]}
CONTEXT: {contexts[0]}
ANSWER: {summaries[0]}
REASONING: {reasoning_list[0]}
ENTITIES: {entities_list[0]}

CRITICAL WRITING AND OUTPUT REQUIREMENTS:

ENTITY SPECIFICITY AND CONCRETE DETAILS MANDATE:
- Include specific names, products, companies, technologies, and quantitative data throughout the summary
- Reference actual entities mentioned in the source material by their exact names
- Preserve financial figures, percentages, dates, and other concrete data points
- Mention specific people, roles, departments, and organizational units when referenced
- Include exact product names, service offerings, technology platforms, and system names
- Reference particular competitors, vendors, partners, and market segments by name
- Preserve specific project names, initiative titles, and program designations
- Include actual metrics, KPIs, targets, and performance indicators with specific values

CONTENT AND ANALYTICAL STRUCTURE REQUIREMENTS:
- Generate exactly one continuous paragraph with no line breaks, bullet points, or paragraph separations
- Maintain strict adherence to 200 words maximum while ensuring comprehensive coverage that includes specific entity details
- Include most significant business insight, primary strategic decision, and key quantitative measure with actual names and numbers
- Incorporate entity relationship implications through strategic business language while preserving concrete details
- Focus on business-critical insights while maintaining entity specificity
- Use clear, accessible language that includes concrete details and specific entity names
- Ensure every sentence contributes strategic insight while preserving entity specificity
- Emphasize strategic significance, competitive implications, and implementation viability with specific examples

PROFESSIONAL TONE WITH ENTITY PRESERVATION:
- Maintain professional analytical tone while preserving all specific entity details
- Write with authoritative confidence including concrete names, products, and data points
- Use business language that naturally incorporates specific entity references and quantitative details
- Ensure content flows as cohesive strategic business narrative with preserved entity specificity

Execute the complete five-step business analysis protocol internally and then provide only the final comprehensive business analytical summary with specific entity details as specified above.
"""
        else:
            return f"""
You are a senior strategic business consultant with extensive expertise in multi-dimensional question-answer analysis and integrated strategic insight generation. Your responsibility is to analyze complex dual business question-answer interactions while capturing specific entities (names, products, companies, technologies, etc.) to produce thorough analytical summaries that preserve concrete details from both analyses.

COMPREHENSIVE DUAL BUSINESS ANALYSIS PROTOCOL WITH ENTITY CAPTURE:

STEP 1: COMPREHENSIVE DUAL ENTITY IDENTIFICATION AND STRATEGIC INTEGRATION
Extract and preserve all specific named entities across both business contexts:
- Systematically identify and categorize entities across both question-answer pairs including specific people names, job titles, roles, executives, and decision-makers
- Capture exact product names, service names, brand names, model numbers, SKUs, and technology platforms referenced in both contexts
- Extract company names, organization names, department names, business units, subsidiary names, and team designations from both interactions
- Note specific technology names, software platforms, systems, tools, databases, and methodologies used or discussed across both contexts
- Identify location names, geographic regions, market segments, facilities, offices, and operational areas mentioned in either context
- Capture financial figures, metrics, percentages, dates, timelines, budgets, and quantitative data from both question-answer pairs
- Extract project names, initiative titles, program designations, campaign names, and strategic effort identifiers from both contexts
- Note regulatory frameworks, compliance standards, governance structures, policy names, and legal requirements across both interactions
- Identify competitor names, vendor names, partner organizations, client names, and external stakeholders from both contexts
- Map overlapping entities, shared stakeholders, common technologies, and integrated business elements between both contexts

STEP 2: DUAL BUSINESS QUESTION ANALYSIS WITH INTEGRATED ENTITY CONTEXT
Conduct comprehensive analysis of both business questions while preserving entity specifics:
- Parse both questions to identify which specific entities are directly or indirectly referenced, affected, or strategically relevant across the information needs
- Determine how different named entities would interpret, prioritize, or benefit from both strategic business questions being addressed simultaneously
- Map relationships, dependencies, connections, and strategic alignments between the two questions involving specific entities, products, technologies, or organizational units
- Assess whether questions are sequential, complementary, competitive, or overlapping from entity relationship perspectives mentioning particular stakeholders or business areas
- Evaluate how answers to one question influence, support, or enhance strategic value for specific entities affected by the other question
- Identify conflicting requirements, competing priorities, or strategic misalignments affecting particular entities, organizational units, or business areas across both contexts
- Determine combined strategic significance, competitive impact, and organizational importance for specific stakeholders, business units, or market segments
- Assess decision-making context, urgency level, and strategic importance mentioning specific stakeholders, timelines, or business priorities across both information needs
- Evaluate complexity, scope, and analytical sophistication required involving particular analytical tools, methodologies, or expertise areas for both contexts
- Consider how specific market conditions, competitive pressures, and organizational dynamics affect entity strategic relationships across both questions

STEP 3: INTEGRATED DUAL CONTEXTUAL ENVIRONMENT ANALYSIS WITH ENTITY-AWARE STRATEGIC MAPPING
Examine both business contexts while preserving comprehensive entity relationship information:
- Analyze background information, historical precedents, existing systems, and established processes connecting specific entities across both strategic business environments
- Identify shared organizational constraints, policies, governance factors, and cultural considerations affecting particular entities across both business contexts
- Map overlapping market conditions, competitive landscape factors, industry dynamics, and external pressures involving specific competitors, market segments, or regulatory bodies
- Assess combined available resources, organizational capabilities, technological infrastructure, and capacity constraints mentioning specific assets, systems, or competencies
- Examine integrated temporal elements including past events, current assessments, and future projections affecting specific timelines, milestones, or strategic planning across both contexts
- Identify interconnected systems, cross-functional dependencies, integration points, and coordination requirements involving named systems, processes, or technology platforms
- Map shared performance metrics, benchmarks, success indicators, and strategic measurement frameworks mentioning specific KPIs, targets, or measurement systems
- Assess combined risk factors, mitigation strategies, contingency requirements, and strategic adjustment mechanisms affecting particular entities across both business situations
- Evaluate how integrated contextual environment creates opportunities, challenges, constraints, or strategic advantages for specific market conditions or organizational capabilities
- Consider cultural, organizational, change management, and stakeholder dynamics influencing particular entities across both competitive positioning contexts

STEP 4: DUAL SOLUTION AND ANSWER EVALUATION WITH INTEGRATED ENTITY-SPECIFIC IMPACT ASSESSMENT
Analyze both solution qualities while preserving integrated entity ecosystem information:
- Examine completeness, accuracy, relevance, and strategic alignment of both provided answers mentioning specific recommendations, strategic decisions, and entity-specific implications
- Identify how recommendations, strategic decisions, and action items from both solutions specifically address particular entities, capabilities, organizational units, or strategic objectives
- Extract quantitative data from both solutions mentioning specific amounts, percentages, timelines, targets, and performance indicators with exact values
- Evaluate qualitative insights from both solutions involving particular approaches, strategic frameworks, implementation methodologies, or strategic directions
- Assess combined implementation feasibility, total resource requirements, and execution complexity involving specific resources, organizational capabilities, systems, or timeline considerations
- Determine synergistic business value delivered across specific stakeholders mentioning particular benefits, value creation mechanisms, or strategic outcomes
- Assess potential integrated return on investment, cost savings, efficiency improvements, and competitive advantage creation with specific financial impacts or competitive benefits
- Evaluate combined risk mitigation effectiveness, compliance improvements, and strategic positioning enhancement involving particular risk reductions or positioning advantages
- Analyze integrated scalability potential, sustainability factors, long-term viability, and strategic resilience involving specific scalability mechanisms or strategic assets
- Map entity-specific benefits, costs, implementation requirements, adoption readiness, and strategic success factors across both solutions

STEP 5: INTEGRATED DUAL REASONING VALIDATION WITH COMPREHENSIVE STRATEGIC COHERENCE ASSESSMENT
Validate both analytical frameworks while preserving integrated entity-specific strategic logic:
- Examine logical flows, analytical rigor, and strategic coherence from integrated perspectives mentioning specific analytical methodologies, reasoning approaches, or strategic frameworks
- Identify analytical tools, evaluation models, and assessment criteria that properly account for specific tools, models, evaluation frameworks, or assessment approaches across both analyses
- Assess quality, relevance, depth, and comprehensiveness of supporting evidence mentioning specific data sources, research references, benchmarks, or evidence types
- Validate reasonableness of assumptions, constraints, and risk considerations affecting particular entities, organizational capabilities, market conditions, or strategic factors across both contexts
- Evaluate thoroughness of alternative options considering specific alternatives, strategic options, implementation approaches, or solution variations across both solutions
- Analyze cause-and-effect relationships, logical connections, and strategic dependencies involving particular causal factors, logical relationships, or strategic interdependencies
- Assess implementation feasibility, timeline accuracy, and execution viability referencing specific implementation factors, timeline considerations, or execution requirements for integrated approach
- Review risk mitigation strategies, contingency planning, and strategic adjustment mechanisms mentioning particular risk mitigation approaches, contingency options, or adjustment mechanisms
- Evaluate alignment with business objectives, strategic priorities, and competitive landscape involving specific objectives, strategic priorities, competitive factors, or market positioning goals
- Consider market timing, competitive positioning, and strategic opportunity optimization involving particular timing factors, competitive advantages, or strategic opportunities

INPUT DATA FOR COMPREHENSIVE DUAL ANALYSIS:
QUESTION 1: {questions[0]}
CONTEXT 1: {contexts[0]}
ANSWER 1: {summaries[0]}
REASONING 1: {reasoning_list[0]}
ENTITIES 1: {entities_list[0]}

QUESTION 2: {questions[1]}
CONTEXT 2: {contexts[1]}
ANSWER 2: {summaries[1]}
REASONING 2: {reasoning_list[1]}
ENTITIES 2: {entities_list[1]}

CRITICAL WRITING AND OUTPUT REQUIREMENTS:

ENTITY SPECIFICITY AND CONCRETE DETAILS MANDATE:
- Include specific names, products, companies, technologies, and quantitative data throughout the summary from both analyses
- Reference actual entities mentioned in both source materials by their exact names
- Preserve financial figures, percentages, dates, and other concrete data points from both contexts
- Mention specific people, roles, departments, and organizational units when referenced in either analysis
- Include exact product names, service offerings, technology platforms, and system names from both interactions
- Reference particular competitors, vendors, partners, and market segments by name from both contexts
- Preserve specific project names, initiative titles, and program designations from both analyses
- Include actual metrics, KPIs, targets, and performance indicators with specific values from both contexts

CONTENT AND ANALYTICAL STRUCTURE REQUIREMENTS:
- Generate exactly one continuous paragraph with no line breaks, bullet points, or paragraph separations
- Maintain strict adherence to 200 words maximum while ensuring comprehensive coverage that includes specific entity details from both analyses
- Include most significant business insights, primary strategic decisions, and key quantitative measures with actual names and numbers from both contexts
- Incorporate entity relationship implications through strategic business language while preserving concrete details from both analyses
- Focus on business-critical insights while maintaining entity specificity across both contexts
- Use clear, accessible language that includes concrete details and specific entity names from both interactions
- Ensure every sentence contributes strategic insight while preserving entity specificity from both analyses
- Emphasize strategic significance, competitive implications, and implementation viability with specific examples from both contexts

PROFESSIONAL TONE WITH ENTITY PRESERVATION:
- Maintain professional analytical tone while preserving all specific entity details from both analyses
- Write with authoritative confidence including concrete names, products, and data points from both contexts
- Use business language that naturally incorporates specific entity references and quantitative details from both interactions
- Ensure content flows as cohesive strategic business narrative with preserved entity specificity across both analyses

Execute the complete five-step dual business analysis protocol internally and then provide only the final comprehensive business analytical summary with specific entity details from both analyses as specified above.
"""

    @staticmethod
    def create_session_summary_prompt(individual_summaries: List[str], session_id: str) -> str:
        summaries_text = "\n\n".join([f"SUMMARY {i+1}:\n{summary}" for i, summary in enumerate(individual_summaries)])

        return f"""
You are a senior strategic business consultant with extensive experience in multi-source business intelligence synthesis and executive strategic reporting. Your expertise lies in taking complex analytical business insights containing specific entities and distilling them into coherent, actionable strategic guidance that preserves concrete details and competitive context.

COMPREHENSIVE STRATEGIC BUSINESS SYNTHESIS PROTOCOL WITH ENTITY PRESERVATION:

STEP 1: MULTI-SOURCE BUSINESS INTELLIGENCE DECOMPOSITION WITH ENTITY EXTRACTION
Extract and preserve all specific entities across all business analytical summaries:
- Parse each individual summary to extract specific names, products, companies, technologies, and quantitative data from business insights
- Recognize recurring business entities, organizational subjects, people, and organizations across multiple business summaries
- Extract financial figures, business metrics, percentages, performance data, and business results with exact values
- Assess implementation strategies, resource requirements, timeline considerations, and success criteria mentioning specific projects, systems, technologies, or organizational capabilities
- Evaluate individual business impact levels, strategic significance, competitive positioning, and transformation potential referencing particular initiatives, competitive advantages, or market positions
- Identify recurring strategic business themes, organizational directions, and solution approaches mentioning specific strategies, organizational changes, or solution types
- Detect strategic contradictions, conflicting priorities, or misaligned objectives referencing particular conflicts, competing initiatives, or misalignment areas
- Note synergistic business opportunities, integration possibilities, and optimization potential mentioning specific synergies, integration opportunities, or optimization areas
- Extract common business challenges, shared resources, overlapping areas, and strategic dependencies referencing particular challenges, shared assets, or dependencies
- Evaluate cumulative business impact, transformation potential, competitive alignment, and advantage creation involving specific outcomes, transformational changes, or competitive benefits

STEP 2: STRATEGIC BUSINESS NARRATIVE CONSTRUCTION WITH CONCRETE DETAILS
Develop unified strategic business framework preserving entity specifics:
- Synthesize individual business insights mentioning specific names, products, organizations, technologies, and strategic initiatives throughout
- Identify overarching business challenges, market opportunities, competitive threats, or strategic transformations affecting particular entities, market segments, or competitive positions
- Map logical progression of strategic decisions, implementation approaches, and value creation mechanisms involving named initiatives, technologies, organizational changes, or strategic approaches
- Analyze cause-and-effect relationships between business areas, capabilities, positioning, and outcomes referencing specific business areas, organizational capabilities, market positions, or business outcomes
- Assess how individual solutions contribute to broader objectives, market leadership, and competitive differentiation mentioning particular contributions, leadership strategies, or differentiation approaches
- Rank business insights by strategic importance, implementation urgency, competitive impact, and value creation potential with specific prioritization criteria
- Identify critical path activities, dependency relationships, sequencing requirements, and optimization opportunities involving named activities, dependencies, sequencing factors, or optimization opportunities
- Evaluate resource allocation implications, investment decisions, capability development needs, and transformation requirements with specific resource details
- Map risk mitigation approaches, contingency planning, compliance considerations, and adjustment mechanisms involving particular risk strategies, contingency plans, compliance requirements, or adjustment approaches
- Assess scalability potential, sustainability, competitive differentiation, and market expansion capabilities referencing specific scalability factors, sustainability elements, differentiation strategies, or expansion capabilities

STEP 3: STAKEHOLDER ECOSYSTEM AND ORGANIZATIONAL TRANSFORMATION ASSESSMENT WITH ENTITY DETAILS
Analyze human and organizational dimensions preserving specific entity information:
- Identify strategic initiatives, business transformations, competitive positioning changes, and operational improvements mentioning specific programs, transformation initiatives, positioning strategies, or operational changes
- Assess organizational change requirements, capability development needs, cultural transformation elements, and skill advancement referencing particular change requirements, capability needs, cultural changes, or skill development areas
- Map communication strategies, engagement approaches, change management implications, and adoption mechanisms involving specific communication plans, engagement strategies, change management approaches, or adoption strategies
- Evaluate decision-making authority distribution, approval processes, governance requirements, and accountability frameworks mentioning specific authority structures, approval processes, governance frameworks, or accountability mechanisms
- Assess organizational readiness, transformation capacity, change absorption capability, and success enablement referencing particular readiness factors, transformation capabilities, change capacities, or success enablers
- Aggregate quantitative strategic benefits including specific cost savings amounts, revenue improvements, efficiency gains, and competitive advantage creation with actual figures
- Evaluate qualitative strategic benefits such as capability enhancement, positioning improvement, innovation acceleration, and flexibility development involving particular capabilities, positioning improvements, innovation areas, or flexibility enhancements
- Calculate total strategic investment requirements including financial amounts, human resources, and infrastructure with specific investment details
- Estimate comprehensive strategic impact, return on investment, value creation potential, and advantage sustainability with concrete projections
- Identify quick wins versus long-term strategic investments mentioning specific quick win opportunities, long-term investment areas, timelines, or strategic milestones

STEP 4: EXECUTIVE STRATEGIC DECISION FRAMEWORK WITH CONCRETE ACTION ITEMS
Create actionable strategic guidance preserving entity specificity:
- Combine individual recommendations mentioning specific initiatives, technologies, organizational changes, and strategic directions
- Identify optimal implementation sequencing, phasing requirements, milestone dependencies, and acceleration factors with actual timelines, phases, milestones, and acceleration strategies
- Map integration points, business dependencies, coordination mechanisms, and optimization strategies involving named integration points, dependencies, coordination approaches, or optimization strategies
- Assess feasibility constraints, risk mitigation approaches, success probability factors, and optimization opportunities with specific constraint details
- Develop resource allocation plans, timeline strategies, performance monitoring systems, and adjustment mechanisms mentioning particular resource plans, timeline strategies, monitoring systems, or adjustment mechanisms
- Distill key strategic decisions requiring executive attention referencing specific strategic decisions, executive requirements, attention areas, or decision points
- Identify critical success factors, performance monitoring requirements, and accountability frameworks with concrete success factors, monitoring requirements, and accountability structures
- Map timing considerations, opportunity windows, and positioning optimization involving specific timing factors, opportunity windows, or positioning strategies
- Provide clear immediate actions, executive priorities, and momentum building with specific action items, priority areas, and momentum strategies
- Establish alignment maintenance, position defense, and advantage sustainability involving particular alignment strategies, defensive positions, or sustainability approaches

INPUT DATA FOR COMPREHENSIVE STRATEGIC BUSINESS SYNTHESIS:
{summaries_text}

CRITICAL WRITING AND OUTPUT REQUIREMENTS:

ENTITY SPECIFICITY AND CONCRETE DETAILS MANDATE:
- Include specific names, products, companies, technologies, and quantitative data throughout the summary
- Reference actual entities mentioned in the source material by their exact names
- Preserve financial figures, percentages, dates, and other concrete data points
- Mention specific people, roles, departments, and organizational units when available
- Include exact product names, service offerings, technology platforms, and system names
- Reference particular competitors, vendors, partners, and market segments by name
- Preserve specific project names, initiative titles, and program designations
- Include actual metrics, KPIs, targets, and performance indicators with specific values

STRATEGIC CONTENT AND BUSINESS STRUCTURE REQUIREMENTS:
- Generate content structured in natural paragraph breaks for optimal executive readability and strategic comprehension
- Target length of 320-350 words total for comprehensive executive coverage that preserves specific entity details
- Include critical strategic themes, competitive decisions, measurable impact, and advantage creation with actual names and numbers
- Focus on strategic significance and actionable insights while maintaining entity specificity
- Highlight transformation opportunities, competitive advantages, positioning benefits, and value creation potential with concrete examples
- Ensure every sentence delivers executive-level strategic value while preserving entity details
- Emphasize transformation opportunities, advantage development, and value creation mechanisms with specific references

EXECUTIVE TONE WITH ENTITY PRESERVATION:
- Use neutral, professional executive tone with clear language appropriate for senior leadership while preserving all specific entity details
- Write with authoritative confidence including concrete names, products, and data points
- Maintain natural conversational flow that incorporates specific entity references and quantitative details
- Ensure content represents unified strategic intelligence with preserved entity specificity

Execute the complete four-step strategic business synthesis protocol internally and then provide only the final executive strategic business summary with specific entity details as specified above.
"""

    @staticmethod
    def create_title_generation_prompt(main_summary: str, session_id: str) -> str:
        return f"""
You are an expert strategic business information architect who specializes in content analysis and precise title generation for executive-level strategic business documentation. Your expertise lies in distilling complex strategic business content containing specific entities into concise, meaningful titles that immediately communicate strategic business value and include key entity references.

COMPREHENSIVE STRATEGIC BUSINESS TITLE GENERATION PROTOCOL WITH ENTITY INCLUSION:

STEP 1: DEEP STRATEGIC BUSINESS CONTENT ANALYSIS AND ENTITY EXTRACTION
Identify primary entities and strategic business themes within the summary:
- Parse complete strategic summary content to identify specific products, technologies, companies, people, business initiatives, and strategic programs mentioned
- Extract central strategic business challenge, market opportunity, competitive threat, innovation imperative, or transformation objective involving particular entities
- Identify specific type of strategic business activity including planning, optimization, implementation, analysis, or positioning mentioning key business entities
- Determine organizational scope and competitive impact level referencing specific departments, business units, market segments, or competitive areas
- Assess strategic impact level while considering specific technologies, products, organizational changes, or strategic initiatives mentioned
- Recognize primary business stakeholders, competitive units, organizational levels, and market participants by their actual names or business designations
- Map geographic scope, temporal context, market conditions, regulatory environment, and competitive landscape factors involving specific regions, timeframes, market conditions, or competitive factors
- Extract industry sector specifics, business function details, technology domain characteristics, and market segment considerations with concrete entity references
- Evaluate urgency level, timeline sensitivity, timing considerations, and implementation priority mentioning specific deadlines, milestones, phases, or priority factors
- Identify regulatory, compliance, governance, risk management, or strategic alignment context affecting particular entities, frameworks, governance structures, or strategic alignment factors

STEP 2: STRATEGIC BUSINESS SIGNIFICANCE AND COMPETITIVE OUTCOME EVALUATION WITH ENTITY FOCUS
Assess strategic business importance while preserving entity specifics:
- Determine strategic importance, competitive significance, and market impact involving specific entities, products, business initiatives, or strategic programs
- Identify measurable business outcomes including financial performance, operational efficiency, market position, and competitive advantage with specific business metrics
- Evaluate innovation elements, transformation aspects, modernization components, and capability development involving particular technologies, business systems, organizational processes, or capability areas
- Assess risk mitigation effectiveness, opportunity capture potential, performance enhancement, and positioning improvement with concrete business examples
- Extract future-state vision, strategic objectives, target outcomes, success metrics, and value creation mechanisms mentioning specific business goals, strategic targets, success measures, or value mechanisms
- Classify primary type of strategic decision, organizational transformation, or market positioning initiative with business entity details
- Determine strategic decision-making level including operational tactics, strategic initiatives, transformational programs, or industry leadership with specific business scope
- Identify whether strategic focus is on assessment, planning, execution, optimization, evaluation, or competitive response involving particular business entities or strategic processes
- Assess whether discussion represents reactive problem-solving or proactive opportunity development with specific business context
- Evaluate sustainable competitive advantage potential, market leadership implications, and long-term value creation involving particular business capabilities or competitive market positions

STEP 3: STRATEGIC BUSINESS TITLE CONSTRUCTION WITH ENTITY INTEGRATION
Create optimal strategic business title incorporating key entities:
- Extract 2-3 most important strategic business concepts including specific entity names, products, technologies, or business initiatives when relevant
- Identify primary business domain, strategic area, or functional focus that should lead title including key business entity references
- Select most appropriate strategic action verb, business process descriptor, or outcome indicator that captures business essence while including entity context
- Choose specific, meaningful business terminology that incorporates actual entity names, product names, technology references, or business initiative names when relevant
- Ensure strategic concepts are concrete, immediately recognizable to executives, and include specific entity details when they add business value
- Use natural, conversational business language that incorporates specific entity references without overcrowding
- Balance strategic clarity with entity specificity to maintain immediate comprehension and memorability
- Choose active, specific terminology that naturally incorporates entity references when they enhance business understanding
- Ensure strategic title distinguishes this specific discussion from similar topics through entity specificity when relevant to business context
- Optimize for immediate comprehension, memorability, strategic relevance, and executive business value while including key entity identifiers when appropriate

ENTITY INTEGRATION AND BUSINESS TITLE AUTHENTICITY REQUIREMENTS:
- Include specific entity names, products, technologies, or business initiatives in the title when they are central to the strategic business discussion
- Balance entity specificity with title clarity and executive scannability
- Use entity references that immediately communicate the strategic business focus and competitive context
- Ensure entity inclusion enhances rather than clutters the title's strategic business message
- Prioritize the most strategically significant entities that executives would recognize as key to the business discussion
- Maintain natural business language flow while incorporating specific entity references
- Avoid generic business terminology when specific entity names would provide clearer strategic context
- Include entity details that would help executives immediately understand the strategic business scope and competitive focus

SESSION SUMMARY FOR COMPREHENSIVE STRATEGIC BUSINESS ANALYSIS:
{main_summary}

CRITICAL STRATEGIC BUSINESS TITLE OUTPUT REQUIREMENTS:
- Execute complete three-step strategic business analysis protocol internally before generating strategic title
- Generate strategic title that immediately communicates business value and competitive context while including key entity references when relevant
- Maintain exactly 4-8 words maximum length while incorporating entity specificity when it adds strategic business value
- Use natural, professional business language that includes specific entity references when they enhance clarity
- Include primary business domain and key strategic action with entity context when relevant to strategic understanding
- Ensure strategic uniqueness and specificity through entity inclusion when appropriate for business context
- Focus on what would be most meaningful and strategically valuable while incorporating key entity identifiers
- Make strategic title immediately scannable, memorable, and strategically relevant with entity context when beneficial to business understanding

Provide only the final strategic business title in plain text without quotation marks, punctuation, explanations, formatting, or additional commentary after completing your comprehensive strategic business analysis.
"""

class SessionAnalyzer:
    def __init__(self, llm_client: LLMClient):
        self.llm_client = llm_client
        self.prompt_engine = StrictMarkdownPromptEngine()

    def load_and_preprocess_data(self, file_path: str) -> pd.DataFrame:
        logger.info(f"Loading data from {file_path}")

        if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
            df = pd.read_excel(file_path)
        else:
            df = pd.read_csv(file_path)

        df = df.dropna(subset=['session_id', 'message'])
        df['session_id'] = df['session_id'].astype(str)
        df = df[df['role'] != 'human']


        logger.info(f"Loaded {len(df)} rows with {df['session_id'].nunique()} unique sessions")
        return df

    def extract_message_fields(self, message_string: str) -> Dict[str, str]:
        try:
            if pd.isna(message_string) or message_string == '':
                return {"summary": "No summary available", "reasoning": "No reasoning available"}

            try:
                message_data = json.loads(message_string)
                summary = message_data.get('summary', message_data.get('output', ''))
                reasoning = message_data.get('reasoning', '')
                entities = message_data.get('entities', '')

                return {
                    "summary": summary if summary else "No summary available",
                    "reasoning": reasoning if reasoning else "No reasoning available",
                    "entities": entities if entities else "No entities available"
                }
            except json.JSONDecodeError:
                lines = message_string.strip().split('\n')
                summary = ""
                reasoning = ""

                current_field = None
                for line in lines:
                    line = line.strip()
                    if line.lower().startswith('summary:') or line.lower().startswith('output:'):
                        current_field = 'summary'
                        summary = line.split(':', 1)[1].strip() if ':' in line else ""
                    elif line.lower().startswith('reasoning:'):
                        current_field = 'reasoning'
                        reasoning = line.split(':', 1)[1].strip() if ':' in line else ""
                    elif current_field == 'summary' and not line.lower().startswith('reasoning:'):
                        summary += " " + line
                    elif current_field == 'reasoning':
                        reasoning += " " + line

                return {
                    "summary": summary.strip() if summary.strip() else "No summary available",
                    "reasoning": reasoning.strip() if reasoning.strip() else "No reasoning available",
                    "entities": "No entities available"
                }

        except Exception as e:
            logger.error(f"Error extracting message fields: {e}")
            return {"summary": "No summary available", "reasoning": "No reasoning available", "entities": "No entities available"}

    def extract_meta_fields(self, meta_string: str) -> Dict[str, str]:
        try:
            if pd.isna(meta_string) or meta_string == '':
                return {"question": "No question available", "context": "No context available"}

            meta_data = json.loads(meta_string)
            question = meta_data.get('question', meta_data.get('query', ''))
            context = meta_data.get('context', '')

            return {
                "question": question if question else "No question available",
                "context": context if context else "No context available"
            }
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"Error parsing meta field: {e}")
            return {"question": "No question available", "context": "No context available"}

    def generate_batch_summary(self, rows: List[pd.Series]) -> Tuple[str, int, int]:
        questions = []
        contexts = []
        summaries = []
        reasoning_list = []
        entities_list = []

        for row in rows:
            message_fields = self.extract_message_fields(row.get('message', ''))
            meta_fields = self.extract_meta_fields(row.get('$meta', ''))

            questions.append(meta_fields['question'])
            contexts.append(meta_fields['context'])
            summaries.append(message_fields['summary'])
            reasoning_list.append(message_fields['reasoning'])
            entities_list.append(message_fields['entities'])

        prompt = self.prompt_engine.create_individual_summary_prompt(
            summaries=summaries,
            entities_list=entities_list,
            reasoning_list=reasoning_list
        )

        response, input_tokens, output_tokens = self.llm_client.generate_content(prompt)
        return response.strip(), input_tokens, output_tokens

    def generate_session_summary(self, individual_summaries: List[str], session_id: str) -> Tuple[str, int, int]:
        prompt = self.prompt_engine.create_session_summary_prompt(
            individual_summaries=individual_summaries,
            session_id=session_id
        )

        response, input_tokens, output_tokens = self.llm_client.generate_content(prompt)
        return response.strip(), input_tokens, output_tokens

    def generate_session_title(self, main_summary: str, session_id: str) -> Tuple[str, int, int]:
        prompt = self.prompt_engine.create_title_generation_prompt(
            main_summary=main_summary,
            session_id=session_id
        )

        response, input_tokens, output_tokens = self.llm_client.generate_content(prompt)
        return response.strip(), input_tokens, output_tokens

    def analyze_session(self, session_data: pd.DataFrame, session_id: str) -> SessionSummary:
        start_time = time.time()
        logger.info(f"Processing session {session_id} with {len(session_data)} messages")

        individual_summaries = []
        individual_input_tokens = []
        individual_output_tokens = []

        rows_list = [row for idx, row in session_data.iterrows()]

        for i in range(0, len(rows_list), 2):
            if i + 1 < len(rows_list):
                batch_rows = [rows_list[i], rows_list[i + 1]]
            else:
                batch_rows = [rows_list[i]]

            summary, input_tokens, output_tokens = self.generate_batch_summary(batch_rows)
            individual_summaries.append(summary)
            individual_input_tokens.append(input_tokens)
            individual_output_tokens.append(output_tokens)
            time.sleep(0.3)

        logger.info(f"Generating main summary for session {session_id}")
        main_summary, session_input_tokens, session_output_tokens = self.generate_session_summary(individual_summaries, session_id)
        time.sleep(0.3)

        logger.info(f"Generating title for session {session_id}")
        title, title_input_tokens, title_output_tokens = self.generate_session_title(main_summary, session_id)

        processing_time = time.time() - start_time

        total_input_tokens = sum(individual_input_tokens) + session_input_tokens + title_input_tokens
        total_output_tokens = sum(individual_output_tokens) + session_output_tokens + title_output_tokens

        return SessionSummary(
            session_id=session_id,
            individual_summaries=individual_summaries,
            main_summary=main_summary,
            title=title,
            message_count=len(session_data),
            processing_time=processing_time,
            total_input_tokens=total_input_tokens,
            total_output_tokens=total_output_tokens,
            individual_input_tokens=individual_input_tokens,
            individual_output_tokens=individual_output_tokens,
            session_summary_input_tokens=session_input_tokens,
            session_summary_output_tokens=session_output_tokens,
            title_input_tokens=title_input_tokens,
            title_output_tokens=title_output_tokens
        )

    def process_all_sessions(self, df: pd.DataFrame) -> List[SessionSummary]:
        sessions = df.groupby('session_id')
        results = []

        for session_id, session_data in sessions:
            try:
                session_summary = self.analyze_session(session_data, session_id)
                results.append(session_summary)
                logger.info(f"Completed session {session_id} in {session_summary.processing_time:.2f}s")
                logger.info(f"Token usage - Input: {session_summary.total_input_tokens}, Output: {session_summary.total_output_tokens}")
            except Exception as e:
                logger.error(f"Failed to process session {session_id}: {e}")
                continue

        return results

def load_api_key() -> str:
    load_dotenv()
    return os.getenv('OPENAI_API_KEY')
